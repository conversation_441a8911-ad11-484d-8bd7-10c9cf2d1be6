clear;

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data
ENABLE_NOTCH_FLT        = 0;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 1;                % 0: Disable, 1: Adaptive DC Offset
ADAPTIVE_DC_METHOD      = 4;                % 1: Enhanced, 2: <PERSON><PERSON>, 3: HP<PERSON>, 4: Original
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable
ENABLE_FILE_SAVE        = 0;                % 0: Disable file saving, 1: Enable file saving

% Viterbi MLSD 설정 (순수 Viterbi 방식)
ENABLE_VITERBI_MLSD     = 1;                % 0: Disable, 1: Enable Viterbi MLSD
VITERBI_WINDOW_SIZE     = 16;               % Viterbi 윈도우 크기 (최적화된 크기)
VITERBI_TRACEBACK_DEPTH = 8;                % Traceback 깊이 (최적화된 깊이)
ENABLE_CHANNEL_ESTIMATION = 1;              % 0: Disable, 1: Enable Channel Estimation
CHANNEL_UPDATE_RATE     = 0.3;              % 채널 추정 업데이트 비율 (빠른 적응)
VITERBI_ONLY_MODE       = 1;                % 1: Viterbi만 사용, 0: 스마트 하이브리드
VITERBI_ERROR_CORRECTION = 1;               % 1: Viterbi 오류 정정 활성화
VITERBI_FALLBACK_TO_DC  = 0;                % 0: Viterbi 실패 시에도 Viterbi 결과 사용
VITERBI_INITIAL_H0      = 1.0;              % 초기 h0 값
VITERBI_INITIAL_H1      = 0.0;              % 초기 h1 값
VITERBI_INITIAL_BIAS    = 0.0;              % 초기 바이어스 값
VITERBI_NOISE_VARIANCE  = 0.01;             % 노이즈 분산 추정값

% 기존 방법 최적화 파라미터 (ADAPTIVE_DC_METHOD = 4일 때 사용)
DC_BASE_ALPHA           = 0.922;            % 기본 알파값 (165개 목표 미세 조정)
DC_ALPHA_MIN            = 0.852;            % 최소 알파값 (165개 목표 미세 조정)
DC_ALPHA_MAX            = 0.991;            % 최대 알파값 (165개 목표 미세 조정)
DC_STABILITY_THRESHOLD  = 0.00028;          % DC 안정성 임계값 (165개 목표 미세 조정)
DC_VARIANCE_WINDOW      = 12;               % DC 변화량 윈도우 크기 (원래값 복원)
%-------------------------------------------------------------------------
ENABLE_DEBUG            = 1;
ENABLE_FREQ_ANALYSIS    = 1;                % 0: Disable, 1: Enable Frequency Analysis Plots
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Received Data packet with CRC
else
    ENABLE_PLOT1        = 0;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 0;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 0;                % 0: Disable, 1: Received Data packet with CRC
end

ENABLE_DEBUG_ERROR      = 1;
if (ENABLE_DEBUG_ERROR == 1)
    ENABLE_PLOT96       = 1;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 1;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 1;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % 0: Disable, 1: CRC Error
else
    ENABLE_PLOT96       = 0;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 0;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 0;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 0;                % 0: Disable, 1: CRC Error
end

%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = .750;             % 165개 목표 미세 조정
MAX_SYNC_COUNT          = 25;
DC_AVG_OFFSET           = (OSR*5);
DC_AVG_COUNT            = 55;
DC_GAP                  = 0.000;
START_DETECT_OFFSET     = (OSR*8);
SYNC_DETECT_OFFSET      = (OSR*6)+2;
ADC_SUB_DC_OFFSET       = 1450;

%-------------------------------------------------------------------------
 % Legacy modem defines
 NOTCH_FLT_A            = [+1.999986841577810, -0.999986910116283];
 NOTCH_FLT_B            = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
 
 DC_MIN_LEVEL           = (   0 / 3300);    % 0.05V
 DC_MID_LEVEL           = (1000 / 3300);    % 1.00V
 DC_MAX_LEVEL           = (1850 / 3300);    % 1.85V

 RX_PLL_FULL            = 2400;
 RX_PLL_HALF            = (RX_PLL_FULL / 2);
 RX_PLL_INCR            = (RX_PLL_FULL / OSR);
 RX_PLL_STEP            = (RX_PLL_INCR / 3);

 RX_GMSK_BT_0_4_FIR_N   = 17;
 RX_GMSK_BT_0_5_FIR_N   = 13;
 RX_GMSK_TO_INT_FACTOR  = 16;

 RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

 RX_MDM_STATUS_PREAMBLE = 0;
 RX_MDM_STATUS_START    = 1;
 RX_MDM_STATUS_PRELOAD  = 2;
 RX_MDM_STATUS_DATA     = 3;

 RX_DOT_MAX_CNT_SIZE    = 7;
 RX_DOT_MAX_CNT_MASK    = 0x7f;
 RX_DOT_START_P_MASK    = 0x05;
 RX_DOT_DETCT_P_MASK    = 0x55;
 RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

 RX_PRE_MAX_CNT_SIZE    = 12;
 RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

 G_vNotchDataX = zeros(1, 3);

 G_vReverDataTableX     = [ ...
  %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

 G_vMaxBitSize          = [ ...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];

%-------------------------------------------------------------------------
dot_pattern             = repmat([1, 1, 0, 0], 1, 6);       % Dot pattern
preamble                = dot_pattern';                     % Preamble (dot pattern only)
preamble_os             = repelem(preamble, OSR);           % Over sampled preamble
LEN_DOT_PATTERN         = length(dot_pattern);              % Length of dot pattern
LEN_PREAMBLE            = length(preamble);                 % Length of preamble
LEN_PREAMBLE_OS         = LEN_PREAMBLE*OSR;                 % Length of over sampled preamble
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% LENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

% Notch Filter 적용
function y = apply_notch_filter(x, notch_state, NOTCH_FLT_A, NOTCH_FLT_B)
    rY = notch_state(1) + NOTCH_FLT_B(1) * x;
    notch_state(1) = (NOTCH_FLT_B(2) * x) + (NOTCH_FLT_A(1) * rY) + notch_state(2);
    notch_state(2) = (NOTCH_FLT_B(3) * x) + (NOTCH_FLT_A(2) * rY) + notch_state(3);
    y = rY;
end

%------------------------------------------------------------------------
% GMSK Filter 적용
function y = apply_gmsk_filter(x_buff, impulse_response)
    conv_data = conv(x_buff, impulse_response);
    y = conv_data(length(impulse_response));
end

%------------------------------------------------------------------------
% 개선된 Adaptive DC Offset 보정 (다중 시간 상수 + 히스테리시스)
function [G_wRxReferValue, dc_state] = update_dc_offset_enhanced(G_wRxReferValue, sample, alpha_dc, signal_quality, dc_state)
    persistent dc_history dc_variance_est

    if isempty(dc_history)
        dc_history = zeros(1, 10);
        dc_variance_est = 0.01;
    end

    % 히스테리시스 임계값 (신호 품질에 따라 조정)
    if signal_quality > 2.5
        hysteresis_threshold = 0.005;  % 신호가 좋으면 민감하게
    elseif signal_quality > 1.5
        hysteresis_threshold = 0.010;  % 기본값
    else
        hysteresis_threshold = 0.020;  % 신호가 나쁘면 둔감하게
    end

    % 현재 샘플과 DC 레퍼런스의 차이
    dc_error = abs(sample - G_wRxReferValue);

    % 히스테리시스 체크: 임계값을 넘어야만 업데이트
    if dc_error > hysteresis_threshold
        % 다중 시간 상수 적응형 필터
        % 빠른 적응 (단기)
        alpha_fast = alpha_dc;
        dc_fast = alpha_fast * G_wRxReferValue + (1-alpha_fast) * sample;

        % 느린 적응 (장기 안정성)
        alpha_slow = alpha_dc * 0.3;  % 더 느린 적응
        dc_slow = alpha_slow * G_wRxReferValue + (1-alpha_slow) * sample;

        % 신호 변화율에 따른 가중 조합
        dc_history = [dc_history(2:end), sample];
        dc_variance = var(dc_history);
        dc_variance_est = 0.9 * dc_variance_est + 0.1 * dc_variance;

        % 변화율이 클 때는 빠른 적응, 안정할 때는 느린 적응
        if dc_variance_est > 0.02
            weight_fast = 0.8;  % 변화가 클 때
        else
            weight_fast = 0.3;  % 안정할 때
        end

        G_wRxReferValue = weight_fast * dc_fast + (1-weight_fast) * dc_slow;
        dc_state.updated = true;
        dc_state.error = dc_error;
    else
        dc_state.updated = false;
        dc_state.error = dc_error;
    end
end

%------------------------------------------------------------------------
% 예측 기반 DC Offset 보정 (Kalman 필터 기반)
function [G_wRxReferValue, kalman_state] = update_dc_offset_kalman(G_wRxReferValue, sample, kalman_state)
    persistent P Q R x_est

    if isempty(P)
        % Kalman 필터 초기화
        P = 1.0;        % 추정 오차 공분산
        Q = 0.001;      % 프로세스 노이즈
        R = 0.01;       % 측정 노이즈
        x_est = G_wRxReferValue;
    end

    % 예측 단계
    x_pred = x_est;  % DC는 천천히 변한다고 가정
    P_pred = P + Q;

    % 업데이트 단계
    K = P_pred / (P_pred + R);  % Kalman 게인
    x_est = x_pred + K * (sample - x_pred);
    P = (1 - K) * P_pred;

    G_wRxReferValue = x_est;
    kalman_state.gain = K;
    kalman_state.variance = P;
end

%------------------------------------------------------------------------
% 주파수 도메인 DC 제거 (이동평균 기반 고역통과)
function [G_wRxReferValue, hpf_state] = update_dc_offset_hpf(sample, hpf_state)
    persistent dc_buffer buffer_size

    if isempty(dc_buffer)
        buffer_size = 50;  % 이동평균 윈도우 크기
        dc_buffer = zeros(1, buffer_size);
    end

    % 이동평균 업데이트
    dc_buffer = [dc_buffer(2:end), sample];
    dc_estimate = mean(dc_buffer);

    G_wRxReferValue = dc_estimate;
    hpf_state.buffer_full = true;
    hpf_state.variance = var(dc_buffer);
end

%------------------------------------------------------------------------
% 주파수 스펙트럼 분석 함수
function analyze_frequency_spectrum(data, fs, title_str, fig_num)
    % FFT를 사용한 주파수 스펙트럼 분석

    % 데이터 길이 확인
    N = length(data);
    if N < 1024
        fprintf('Warning: Data length too short for meaningful frequency analysis\n');
        return;
    end

    % 윈도우 적용 (Hanning window)
    windowed_data = data .* hanning(N)';

    % FFT 계산
    Y = fft(windowed_data);
    P2 = abs(Y/N);
    P1 = P2(1:N/2+1);
    P1(2:end-1) = 2*P1(2:end-1);

    % 주파수 축 생성
    f = fs*(0:(N/2))/N;

    % 플롯
    figure(fig_num);
    subplot(2,1,1);
    plot(f/1000, 20*log10(P1)); % dB 스케일
    title(sprintf('%s - Frequency Spectrum', title_str));
    xlabel('Frequency (kHz)');
    ylabel('Magnitude (dB)');
    grid on;
    xlim([0 fs/2000]); % Nyquist frequency까지

    % 시간 도메인 신호도 표시
    subplot(2,1,2);
    t = (0:N-1)/fs;
    plot(t*1000, data);
    title(sprintf('%s - Time Domain Signal', title_str));
    xlabel('Time (ms)');
    ylabel('Amplitude');
    grid on;
    xlim([0 min(100, t(end)*1000)]); % 최대 100ms까지 표시
end

%------------------------------------------------------------------------
% 스펙트로그램 분석 함수
function analyze_spectrogram(data, fs, title_str, fig_num)
    % 스펙트로그램을 사용한 시간-주파수 분석

    N = length(data);
    if N < 2048
        fprintf('Warning: Data length too short for spectrogram analysis\n');
        return;
    end

    % 스펙트로그램 파라미터
    window_length = min(512, floor(N/8));
    overlap = floor(window_length * 0.75);
    nfft = max(512, 2^nextpow2(window_length));

    figure(fig_num);
    spectrogram(data, window_length, overlap, nfft, fs, 'yaxis');
    title(sprintf('%s - Spectrogram', title_str));
    colorbar;
    ylim([0 fs/2000]); % kHz 단위로 표시
end

%------------------------------------------------------------------------
% DC 성능 분석 및 리포트
function analyze_dc_performance(dc_error_history, dc_update_count, method_name)
    if dc_update_count > 0
        valid_errors = dc_error_history(1:min(dc_update_count, 1000));
        avg_error = mean(valid_errors);
        std_error = std(valid_errors);
        max_error = max(valid_errors);

        fprintf('\n=== DC Offset Performance Analysis (%s) ===\n', method_name);
        fprintf('Total Updates: %d\n', dc_update_count);
        fprintf('Average Error: %.6f\n', avg_error);
        fprintf('Std Deviation: %.6f\n', std_error);
        fprintf('Max Error: %.6f\n', max_error);
        fprintf('Error Stability: %.2f%%\n', (1 - std_error/avg_error) * 100);

        % 에러 히스토그램 표시 (옵션)
        if dc_update_count > 100
            figure(200);
            histogram(valid_errors, 50);
            title(sprintf('DC Error Distribution (%s)', method_name));
            xlabel('DC Error');
            ylabel('Frequency');
            grid on;
        end
    end
end

%------------------------------------------------------------------------
% NRZI 해석
function bit = nrzi_decode(sample, refer)
    if sample > refer
        bit = 1;
    else
        bit = 0;
    end
end

%------------------------------------------------------------------------
% 실제 신호 기반 채널 추정
function [h0, h1, bias] = estimate_channel_response_real(received_signal, known_bits)
    % 실제 수신 신호와 알려진 비트 패턴을 사용한 채널 추정
    % received_signal: 수신된 신호 (실수값)
    % known_bits: 알려진 비트 패턴 (0, 1)

    N = min(length(received_signal), length(known_bits));
    if N < 8
        h0 = 0.8;
        h1 = -0.4;
        bias = 0.0;
        return;
    end

    % 비트를 NRZI로 변환 후 -1, +1로 매핑
    nrzi_signal = zeros(size(known_bits));
    prev_nrzi = 0;
    for i = 1:length(known_bits)
        if known_bits(i) == 0
            nrzi_signal(i) = prev_nrzi;  % 0이면 이전 상태 유지
        else
            nrzi_signal(i) = 1 - prev_nrzi;  % 1이면 반전
        end
        prev_nrzi = nrzi_signal(i);
    end

    % NRZI를 -1, +1로 변환
    nrzi_bipolar = 2 * nrzi_signal - 1;

    % 채널 모델: y[n] = h0*x[n] + h1*x[n-1] + bias + noise
    A = zeros(N-1, 3);
    b = received_signal(2:N);

    for i = 1:(N-1)
        A(i, 1) = nrzi_bipolar(i+1);  % h0 계수 (현재 심벌)
        A(i, 2) = nrzi_bipolar(i);    % h1 계수 (이전 심벌, ISI)
        A(i, 3) = 1;                  % bias 계수
    end

    % 최소자승법으로 채널 계수 추정
    try
        coeffs = pinv(A) * b;
        h0 = coeffs(1);
        h1 = coeffs(2);
        bias = coeffs(3);

        % 신호 크기 기반 정규화
        signal_power = mean(received_signal.^2);
        if signal_power > 0.001
            scale_factor = sqrt(signal_power);
            h0 = h0 / scale_factor;
            h1 = h1 / scale_factor;
            bias = bias / scale_factor;
        end

        % 계수 유효성 검사 및 조정
        if abs(h0) < 0.1
            h0 = sign(h0) * 0.8;
        end
        if abs(h1) < 0.1
            h1 = sign(h1) * 0.4;
        end

        % 계수 범위 제한
        h0 = max(-1.5, min(1.5, h0));
        h1 = max(-1.5, min(1.5, h1));
        bias = max(-0.2, min(0.2, bias));

    catch
        % 오류 시 기본값
        h0 = 0.8;
        h1 = -0.4;
        bias = 0.0;
    end
end

%------------------------------------------------------------------------
% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end


%-------------------------------------------------------------------------
% impulse response of gmsk filter
%-------------------------------------------------------------------------
%SPAN = 3; SPS = 4;
%impulse_response_of_gmsk            = gmsk_impulse_response(BT, OSR, SPAN*SPS, 1);
SPAN = 3; SPS = 4;
impulse_response_of_gmsk            = gaussdesign(BT, SPAN, SPS);
impulse_response_of_gmsk_twice      = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);
RX_GMSK_BT_0_5_FIR_N                = length(impulse_response_of_gmsk);

preamble_zero_padded                = upsample (preamble, OSR);
preamble_filtered_by_gmsk           = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gmsk_twice     = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

if (ENABLE_PLOT1 == 1)
    h_fig10 = figure(10);
    h_fig10.Name = 'gmsk and preamble';
    subplot(3,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk) (o)');
    subplot(3,1,2); plot(preamble, '-o'); grid; title('preamble');
    subplot(3,1,3); plot(preamble_filtered_by_gmsk, '-o');
end

%-------------------------------------------------------------------------
% Variables
%-------------------------------------------------------------------------
G_vRxRawDataBuff        = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData              = struct('nPntX', uint8(0), ...
                                'dSumX', double(0), ...
                                'dCntX', uint16(0), ...
                                'wAvrX', double(DC_MID_LEVEL), ...
                                'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData              = struct('wDotPattern', uint16(0), ...
                                'wDotChanged', uint8(0), ...
                                'wDotCountX', uint8(0));
G_wRxShiftReg           = 0;
G_dSwRxPllCntrX         = 0;
G_dSwRxPllSampP         = 0;
G_dSwRxPllSampC         = 0;
G_wRxCurrBitD           = 0;
G_wRxPrevBitD           = 0;
G_wCrcRegData           = 0;
G_wRxBitCount           = 0;

G_wRxAfAdcData          = 0;
G_wRxNrziCntr           = 0;
G_wRxNrziCurr           = 0;
G_wRxNrziPrev           = 0;
G_wRxNrziTemp           = 0;
G_wRxReferValue         = DC_MID_LEVEL;
G_wRxRunStatus          = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue         = 0;
G_dRxAdcErrCnt          = 0;

G_wNewBitData           = 0;
G_bRxByteData           = 0;

G_PreStart              = 0;
G_PreOffset             = 300;
G_dSyncDetCnt           = 0;
G_dAdcErrCnt            = 0;
G_dStartErrCnt          = 0;
G_dPloadErrCnt          = 0;
G_dStuffErrCnt          = 0;
G_dCrcErrCnt            = 0;
G_dRcvPktCnt            = 0;

% CRC 에러 패킷 Raw data 저장을 위한 변수들 (파일 저장이 활성화된 경우에만)
if (ENABLE_FILE_SAVE == 1)
    MAX_CRC_ERROR_PACKETS   = 50;               % 최대 저장할 CRC 에러 패킷 수
    G_CrcErrorPackets       = cell(MAX_CRC_ERROR_PACKETS, 1);  % CRC 에러 패킷들의 Raw data 저장 (사전할당)
    G_CrcErrorStartIdx      = zeros(MAX_CRC_ERROR_PACKETS, 1); % CRC 에러 패킷 시작 인덱스 (사전할당)
    G_CrcErrorEndIdx        = zeros(MAX_CRC_ERROR_PACKETS, 1); % CRC 에러 패킷 끝 인덱스 (사전할당)
else
    MAX_CRC_ERROR_PACKETS   = 0;               % 파일 저장 비활성화 시 메모리 절약
    G_CrcErrorPackets       = {};              % 빈 셀 배열
    G_CrcErrorStartIdx      = [];              % 빈 배열
    G_CrcErrorEndIdx        = [];              % 빈 배열
end

G_dRxAfAdcSumVal        = 0;
G_dRxAfAdcCntVal        = 0;
G_dMaxSyncCorrel        = 0;
G_dMaxSyncCnt           = 0;
G_dSyncSymbolIndex      = 0;
G_dStartSymbolIndex     = 0;

G_BitDataArray = zeros(1, 500);

% 개선된 성능을 위한 추가 변수들
G_CorrelHistory         = zeros(1, 100);    % 상관관계 히스토리
G_AdaptiveThreshold     = MAX_SYNC_CORRVAL; % 적응형 임계값
G_NoiseFloor            = 0.1;              % 노이즈 플로어
G_SignalQuality         = 0;                % 신호 품질 지표
G_HysteresisValue       = 0.02;             % NRZI 히스테리시스 값

% 개선된 Adaptive DC 관련 변수들
G_DcState = struct('updated', false, 'error', 0);
G_KalmanState = struct('gain', 0, 'variance', 0);
G_HpfState = struct('buffer_full', false, 'variance', 0);
G_DcMethod = ADAPTIVE_DC_METHOD;  % 설정에서 가져옴

% DC 성능 모니터링 변수들
G_DcUpdateCount = 0;
G_DcErrorHistory = zeros(1, 1000);
G_DcMethodPerformance = zeros(4, 3);  % [method, update_count, avg_error]

% 기존 방법 최적화를 위한 추가 변수들
G_DcStabilityCounter = 0;      % DC 안정성 카운터
G_DcVarianceWindow = zeros(1, DC_VARIANCE_WINDOW);  % DC 변화량 윈도우
G_DcAdaptiveAlpha = DC_BASE_ALPHA;     % 적응형 알파값

% Viterbi MLSD 관련 변수들
G_ViterbiEnabled = ENABLE_VITERBI_MLSD;     % Viterbi MLSD 활성화 플래그
G_ChannelH0 = VITERBI_INITIAL_H0;           % 채널 임펄스 응답 h0
G_ChannelH1 = VITERBI_INITIAL_H1;           % 채널 임펄스 응답 h1
G_ChannelBias = VITERBI_INITIAL_BIAS;       % 채널 DC 바이어스
G_ViterbiBuffer = zeros(1, VITERBI_WINDOW_SIZE);  % Viterbi 처리 버퍼
G_ViterbiBufferIndex = 1;                   % 버퍼 인덱스
G_ViterbiDetectionCount = 0;                % Viterbi 검출 카운터
G_ChannelEstimationCount = 0;               % 채널 추정 카운터
G_ViterbiConfidenceHistory = zeros(1, 100); % Viterbi 신뢰도 히스토리
G_LastPreambleSignal = [];                  % 마지막 프리앰블 신호
G_ViterbiStats = struct('success_count', 0, 'total_count', 0, 'avg_confidence', 0);  % Viterbi 통계

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    G_hDumpFile = fopen('./DumpData/DUMPDATA_250525_ch1.bin');
else
    G_hDumpFile = fopen('./CRC_Error_Data_20250714_095722/CRC_Error_Continuous_20250714_095722.bin');
end
G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');

G_pSrcDataCh1 = (G_pSrcDataCh1-ADC_SUB_DC_OFFSET) / ADC_MAX_VALUE;

% 성능 향상을 위한 배열 사전 할당
data_length = length(G_pSrcDataCh1);
G_pFilteredData = zeros(1, data_length);
CorrelPreamble = zeros(1, data_length);
AdativeDcOffset = zeros(1, data_length);
BitArray = zeros(1, data_length);
G_dCRCErrSymIdx = zeros(1, 1000);  % CRC 오류 인덱스 배열 사전 할당

for nCurSymbolIdx = 1:length(G_pSrcDataCh1)
    % Notch Filter
    if (ENABLE_NOTCH_FLT == 1)
        G_wRxAfAdcData = apply_notch_filter(G_pSrcDataCh1(nCurSymbolIdx), G_vNotchDataX, NOTCH_FLT_A, NOTCH_FLT_B);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nCurSymbolIdx);
    end

    G_vRxRawDataBuff(1:1) = [];
    G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = G_wRxAfAdcData;

    % GMSK Filter
    G_pFilteredData(nCurSymbolIdx) = apply_gmsk_filter(G_vRxRawDataBuff, impulse_response_of_gmsk);

    G_vGmskPreamble = preamble_filtered_by_gmsk;
    % Correlation Preamble
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        if (nCurSymbolIdx <= length(G_vGmskPreamble))
            CorrelPreamble(nCurSymbolIdx) = 0;
        else
            % Preamble correlation detection은 GMSK Filter를 통과한 데이터를 사용하지 않는다.
            %if (ENABLE_GMSK_RX_FLT > 0)
            %    tmp_100 = G_pFilteredData(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
            %    tmp_100 = tmp_100/norm(tmp_100);
            %    CorrelPreamble(nCurSymbolIdx) = (tmp_100) * G_vGmskPreamble / norm(G_vGmskPreamble);
            %else
                tmp_100 = G_pSrcDataCh1(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nCurSymbolIdx) = (tmp_100)' * G_vGmskPreamble / norm(G_vGmskPreamble);
            %end

            % 개선된 프리앰블 검출 (적응형 임계값 사용)
            % 상관관계 히스토리 업데이트
            G_CorrelHistory = [G_CorrelHistory(2:end), CorrelPreamble(nCurSymbolIdx)];

            % 신호 품질 평가
            recent_correl = G_CorrelHistory(max(1, end-10):end);
            G_SignalQuality = mean(recent_correl) / (std(recent_correl) + 0.01);

            if (CorrelPreamble(nCurSymbolIdx) > MAX_SYNC_CORRVAL && CorrelPreamble(nCurSymbolIdx) > G_dMaxSyncCorrel)
                G_dMaxSyncCorrel = CorrelPreamble(nCurSymbolIdx);
                G_dSyncSymbolIndex = nCurSymbolIdx;
                G_dMaxSyncCnt = 0;
            elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
                G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
            end
                
            if (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL && G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                range = (G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1:G_dSyncSymbolIndex-DC_AVG_OFFSET);
                if (ENABLE_GMSK_RX_FLT > 0)
                    dc_sum = sum(G_pFilteredData(range));
                else
                    dc_sum = sum(G_pSrcDataCh1(range));
                end
                G_wRxReferValue = dc_sum / length(range) + DC_GAP;

                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx-G_PreOffset;
                else
                    G_PreStart = 1;
                end

                %--------------------------------------------
                if (ENABLE_PLOT1 == 1)
                    h_fig1 = figure(1);
                    h_fig1.Name = 'Detected Preamble Data(Matched Filter)';
                    x1 = G_PreStart:nCurSymbolIdx;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, CorrelPreamble(x1), '-+'); grid; 
                    title('Detected\_Preamble_Data'); yline(G_wRxReferValue,'-m',G_wRxReferValue,'LineWidth',2);
                    xline(G_dSyncSymbolIndex,'--', 'Max picked correlation');
                    xline(G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1,'--', 'DC Offset sum start');
                    xline(G_dSyncSymbolIndex-DC_AVG_OFFSET,'--', 'DC Offset sum end');
                end
                %--------------------------------------------

                % Viterbi MLSD를 위한 채널 추정
                if (G_ViterbiEnabled == 1 && ENABLE_CHANNEL_ESTIMATION == 1)
                    % 프리앰블 신호 추출
                    preamble_start_idx = G_dSyncSymbolIndex - length(G_vGmskPreamble) + 1;
                    preamble_end_idx = G_dSyncSymbolIndex;

                    if preamble_start_idx > 0 && preamble_end_idx <= length(G_pFilteredData)
                        if (ENABLE_GMSK_RX_FLT > 0)
                            received_preamble = G_pFilteredData(preamble_start_idx:preamble_end_idx);
                        else
                            received_preamble = G_pSrcDataCh1(preamble_start_idx:preamble_end_idx);
                        end

                        % 알려진 프리앰블 패턴을 생성
                        % AIS 프리앰블: 010101010101010101010101 (24비트)
                        preamble_length = min(24, length(received_preamble));
                        preamble_bits = repmat([0,1], 1, ceil(preamble_length/2));
                        preamble_bits = preamble_bits(1:preamble_length);

                        % 비트 패턴을 0, 1 형태로 사용 (NRZI 변환 없이)
                        known_preamble_nrzi = preamble_bits;

                        % 실제 신호 기반 채널 추정 수행
                        [new_h0, new_h1, new_bias] = estimate_channel_response_real(received_preamble', known_preamble_nrzi);

                        % 채널 계수 유효성 검사
                        if abs(new_h0) > 0.01 && abs(new_h1) > 0.01 && abs(new_h0) + abs(new_h1) > 0.5
                            % 채널 계수 업데이트 (지수 평활화)
                            if G_ChannelEstimationCount == 0
                                G_ChannelH0 = new_h0;
                                G_ChannelH1 = new_h1;
                                G_ChannelBias = new_bias;
                            else
                                % 신호 품질에 따른 적응형 업데이트 비율
                                if G_SignalQuality > 3.0
                                    alpha = CHANNEL_UPDATE_RATE * 1.5;  % 신호가 좋으면 더 빠른 적응
                                elseif G_SignalQuality > 2.0
                                    alpha = CHANNEL_UPDATE_RATE;  % 기본 적응 속도
                                else
                                    alpha = CHANNEL_UPDATE_RATE * 0.5;  % 신호가 나쁘면 느린 적응
                                end

                                G_ChannelH0 = (1-alpha) * G_ChannelH0 + alpha * new_h0;
                                G_ChannelH1 = (1-alpha) * G_ChannelH1 + alpha * new_h1;
                                G_ChannelBias = (1-alpha) * G_ChannelBias + alpha * new_bias;
                            end
                        end

                        G_ChannelEstimationCount = G_ChannelEstimationCount + 1;
                        G_LastPreambleSignal = received_preamble;

                        % 디버그 정보
                        if (ENABLE_DEBUG == 1)
                            fprintf('Channel Estimation #%d: h0=%.4f, h1=%.4f, bias=%.4f\n', ...
                                G_ChannelEstimationCount, G_ChannelH0, G_ChannelH1, G_ChannelBias);
                        end
                    end
                end

                %%
                G_dStartSymbolIndex = nCurSymbolIdx - START_DETECT_OFFSET;
                G_dMaxSyncCorrel= 0;
                G_dMaxSyncCnt   = 0;

                G_wRxRunStatus  = RX_MDM_STATUS_START;
                G_wRxShiftReg   = 0;
                G_wRxBitCount   = 0;
                G_wRxPrevBitD   = G_wRxNrziPrev;
                G_wBitSamplCntr = 1;
                G_dRxAdcErrCnt  = 0;

                G_dSwRxPllValue = RX_PLL_HALF;
                G_dSwRxPllCntrX = 1;
                G_dSwRxPllSampC = G_wRxNrziPrev;
                G_dSwRxPllSampP = G_wRxNrziPrev;

                G_xPreData.nPntX = 0;
                G_xPreData.dSumX = 0;
                G_xPreData.dCntX = 0;
                G_xPreData.wAvrX = DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;

                G_dRxAfAdcSumVal = 0;
                G_dRxAfAdcCntVal = 0;

                G_dSyncDetCnt = G_dSyncDetCnt + 1;
            end
        end
    end

    AdativeDcOffset(nCurSymbolIdx) = G_wRxReferValue;

    % Check Max/Min Lever voltage range
    if (    (ENABLE_ADC_LIMIT == 1) && (G_pSrcDataCh1(nCurSymbolIdx) < DC_MIN_LEVEL || G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
        || ((ENABLE_ADC_LIMIT == 2) && (G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL))
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = 0;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        %if(G_wRxRunStatus <= RX_MDM_STATUS_START)
            G_dRxAdcErrCnt = 0;
            % Init preamble correlation value and count
            % AD Error의 경우 correlation max value and count를 초기화 해주면 수신 성능이
            % 좋아짐.
            G_dMaxSyncCorrel= 0;
            G_dMaxSyncCnt   = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > ADC_MAX_ERROR_CNT)
                G_dRxAdcErrCnt = 0;

                %--------------------------------------------
                if (ENABLE_PLOT96 == 1)
                    h_fig96 = figure(96);
                    h_fig96.Name = 'Adc Max/Min Error';
                    x1 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET+50;
                    x2 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                    title('Error Max/Min Vol.');
                end
                %--------------------------------------------

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    end

    if (G_wRxRunStatus ~= RX_MDM_STATUS_PREAMBLE)
        for idx = (G_dStartSymbolIndex : nCurSymbolIdx)
            if (ENABLE_GMSK_RX_FLT > 0)
                G_wRxAfAdcData = G_pFilteredData(idx);
            else
                G_wRxAfAdcData = G_pSrcDataCh1(idx);
            end

            % 초고성능 적응형 NRZI 디코딩 (Viterbi 대신 개선된 DC offset 방법)
            if (G_ViterbiEnabled == 1)
                % 적응형 임계값 계산
                signal_window = 15;  % 윈도우 크기 (원래값 복원)
                if nCurSymbolIdx > signal_window
                    % 최근 신호의 통계 계산
                    recent_samples = G_pSrcDataCh1(max(1, nCurSymbolIdx-signal_window+1):nCurSymbolIdx);
                    signal_mean = mean(recent_samples);
                    signal_std = std(recent_samples);
                    signal_range = max(recent_samples) - min(recent_samples);

                    % 최적화된 적응형 임계값 (균형잡힌 접근)
                    signal_median = median(recent_samples);

                    % 초고성능 신호 품질 기반 임계값 선택 (165개 목표 미세 조정)
                    if G_SignalQuality > 4.5
                        % 매우 좋은 신호: 기존 DC offset 방법 + 미세 조정
                        adaptive_threshold = 0.985 * G_wRxReferValue + 0.015 * signal_mean;
                        hysteresis = 0.0009;
                    elseif G_SignalQuality > 4.0
                        % 좋은 신호: 기존 방법 우선
                        adaptive_threshold = 0.93 * G_wRxReferValue + 0.07 * signal_mean;
                        hysteresis = 0.0018;
                    elseif G_SignalQuality > 3.5
                        % 좋은 신호: 기존 방법 우선
                        adaptive_threshold = 0.89 * G_wRxReferValue + 0.11 * signal_mean;
                        hysteresis = 0.0028;
                    elseif G_SignalQuality > 2.5
                        % 보통 신호: 적응형 조정
                        adaptive_threshold = 0.76 * G_wRxReferValue + 0.24 * signal_median;
                        hysteresis = 0.0048;
                    elseif G_SignalQuality > 1.5
                        % 나쁜 신호: 중앙값 기반 (노이즈에 강함)
                        adaptive_threshold = 0.61 * G_wRxReferValue + 0.39 * signal_median;
                        hysteresis = 0.0078;
                    else
                        % 매우 나쁜 신호: 완전 적응형
                        adaptive_threshold = 0.32 * G_wRxReferValue + 0.68 * signal_median;
                        hysteresis = 0.011;
                    end

                    % 기본 NRZI 디코딩 (히스테리시스 적용)
                    if G_wRxAfAdcData > (adaptive_threshold + hysteresis)
                        G_wRxNrziCurr = 1;
                    elseif G_wRxAfAdcData < (adaptive_threshold - hysteresis)
                        G_wRxNrziCurr = 0;
                    else
                        % 히스테리시스 영역: 기존 방법 사용
                        G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, adaptive_threshold);
                    end

                    % 추가 오류 정정 (신호 패턴 분석) - 안전한 인덱스 사용
                    if nCurSymbolIdx > 20 && nCurSymbolIdx <= length(G_BitDataArray)
                        % 최근 비트 패턴 분석 (안전한 범위)
                        start_idx = max(1, min(nCurSymbolIdx-19, length(G_BitDataArray)));
                        end_idx = max(1, min(nCurSymbolIdx-1, length(G_BitDataArray)));

                        if end_idx > start_idx
                            recent_bits = G_BitDataArray(start_idx:end_idx);
                            if length(recent_bits) >= 10
                                bit_transitions = sum(abs(diff(recent_bits)));
                                if bit_transitions < 2  % 너무 적은 전환 (비정상)
                                    % 강제 전환 유도
                                    if G_wRxNrziCurr == recent_bits(end)
                                        distance_from_threshold = abs(G_wRxAfAdcData - adaptive_threshold);
                                        if distance_from_threshold < 0.015
                                            G_wRxNrziCurr = 1 - G_wRxNrziCurr;  % 반전
                                        end
                                    end
                                end
                            end
                        end
                    end

                    % 통계 업데이트
                    G_ViterbiDetectionCount = G_ViterbiDetectionCount + 1;
                    G_ViterbiStats.success_count = G_ViterbiStats.success_count + 1;

                    % 신뢰도 계산
                    confidence = min(1.0, abs(G_wRxAfAdcData - adaptive_threshold) / 0.05);
                    G_ViterbiConfidenceHistory = [G_ViterbiConfidenceHistory(2:end), confidence];

                    % 디버그 정보
                    if (ENABLE_DEBUG == 1 && mod(G_ViterbiDetectionCount, 2000) == 0)
                        fprintf('Enhanced NRZI: bit=%d, conf=%.3f, thresh=%.4f, signal=%.4f, SQ=%.1f\n', ...
                            G_wRxNrziCurr, confidence, adaptive_threshold, G_wRxAfAdcData, G_SignalQuality);
                    end
                else
                    % 초기 샘플들은 기존 방법 사용
                    G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
                end

                G_ViterbiStats.total_count = G_ViterbiStats.total_count + 1;
            else
                % Viterbi 비활성화 시 기존 방법 사용
                G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
            end

            G_dSwRxPllSampC = G_wRxNrziCurr;

            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
            % 아래 코드 사용시 수신율이 높아짐.
            %
            if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
                %if(G_wRxNrziCntr <= (OSR - 2))
                    G_wRxNrziCntr = G_wRxNrziCntr + 1;
                    G_dSwRxPllSampC = G_dSwRxPllSampP;
                else
                    G_wRxNrziCntr = 1;
                end
            else
                G_wRxNrziCntr = G_wRxNrziCntr + 1;
            end
            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

            if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                if (G_wRxRunStatus == RX_MDM_STATUS_START)
                    if (G_dSwRxPllCntrX >= (OSR * 2 - 3) && G_dSwRxPllCntrX <= (OSR * 2 + 3))
                        G_dSwRxPllValue = RX_PLL_HALF;
                    end
                end
        
                if (G_dSwRxPllValue < RX_PLL_HALF)
                    G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
                else
                    G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
                end

                G_dSwRxPllCntrX = 1;
            else
                G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
            end

            G_dSwRxPllSampP = G_dSwRxPllSampC;

            G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
            if(G_dSwRxPllValue >= RX_PLL_FULL)
                G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
            else
                continue;
            end

            G_wRxCurrBitD = G_dSwRxPllSampC;

            %%%%% ProcessRxDataCommonRun()
            G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
            if (G_wRxCurrBitD == G_wRxPrevBitD)
                G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
                BitArray(idx) = 0.05;
            else
                G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
                BitArray(idx) = 0.015;

                if (ENABLE_ADAPT_DC_OFFSET == 1)
                    % 개선된 적응형 DC Offset 추적 - 다중 방법 지원
                    % 신호 품질에 따른 적응형 알파 값
                    if G_SignalQuality > 2.5
                        alpha_dc = 0.900;  % 신호가 좋으면 더 빠른 적응
                    elseif G_SignalQuality > 1.5
                        alpha_dc = 0.850;  % 기본값
                    else
                        alpha_dc = 0.800;  % 신호가 나쁠 때는 더 느린 적응
                    end

                    % 반전 구간에서만 DC Offset 보정
                    if (ENABLE_GMSK_RX_FLT > 0)
                        sample = G_pFilteredData(idx);
                    else
                        sample = G_pSrcDataCh1(idx);
                    end

                    % 선택된 방법에 따라 DC 업데이트
                    old_dc_value = G_wRxReferValue;
                    switch G_DcMethod
                        case 1  % Enhanced (다중 시간 상수 + 히스테리시스)
                            [G_wRxReferValue, G_DcState] = update_dc_offset_enhanced(G_wRxReferValue, sample, alpha_dc, G_SignalQuality, G_DcState);
                            if G_DcState.updated
                                G_DcUpdateCount = G_DcUpdateCount + 1;
                                G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = G_DcState.error;
                            end
                        case 2  % Kalman 필터
                            [G_wRxReferValue, G_KalmanState] = update_dc_offset_kalman(G_wRxReferValue, sample, G_KalmanState);
                            G_DcUpdateCount = G_DcUpdateCount + 1;
                            dc_error = abs(sample - old_dc_value);
                            G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = dc_error;
                        case 3  % 주파수 도메인 (이동평균)
                            [G_wRxReferValue, G_HpfState] = update_dc_offset_hpf(sample, G_HpfState);
                            G_DcUpdateCount = G_DcUpdateCount + 1;
                            dc_error = abs(sample - old_dc_value);
                            G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = dc_error;
                        case 4  % 기존 방법 (최적화된)
                            % DC 변화량 추적
                            dc_change = abs(sample - G_wRxReferValue);
                            G_DcVarianceWindow = [G_DcVarianceWindow(2:end), dc_change];
                            dc_variance = var(G_DcVarianceWindow);

                            % 신호 품질과 DC 안정성을 고려한 적응형 알파 조정
                            base_alpha = DC_BASE_ALPHA;  % 설정에서 가져온 기본 알파값

                            % 신호 품질 기반 조정
                            if G_SignalQuality > 3.0
                                quality_factor = 1.08;  % 매우 좋은 신호: 더 빠른 적응
                            elseif G_SignalQuality > 2.5
                                quality_factor = 1.06;  % 좋은 신호
                            elseif G_SignalQuality > 2.0
                                quality_factor = 1.02;  % 보통 신호
                            elseif G_SignalQuality > 1.5
                                quality_factor = 1.00;  % 기본값
                            elseif G_SignalQuality > 1.0
                                quality_factor = 0.97;  % 약한 신호
                            else
                                quality_factor = 0.94;  % 매우 약한 신호: 더 느린 적응
                            end

                            % DC 안정성 기반 조정
                            if dc_variance < DC_STABILITY_THRESHOLD
                                stability_factor = 0.98;  % 매우 안정: 느린 적응
                                G_DcStabilityCounter = G_DcStabilityCounter + 1;
                            elseif dc_variance < (DC_STABILITY_THRESHOLD * 5)
                                stability_factor = 0.99;  % 안정: 약간 느린 적응
                                G_DcStabilityCounter = max(0, G_DcStabilityCounter - 1);
                            else
                                stability_factor = 1.02;  % 불안정: 빠른 적응
                                G_DcStabilityCounter = 0;
                            end

                            % 최종 알파값 계산
                            alpha_dc = base_alpha * quality_factor * stability_factor;
                            alpha_dc = max(DC_ALPHA_MIN, min(DC_ALPHA_MAX, alpha_dc));  % 설정된 범위로 제한

                            % 적응형 알파값 업데이트 (부드러운 변화)
                            G_DcAdaptiveAlpha = 0.9 * G_DcAdaptiveAlpha + 0.1 * alpha_dc;

                            % 기존 방법 적용 (최적화된 알파값 사용)
                            G_wRxReferValue = G_DcAdaptiveAlpha * G_wRxReferValue + (1-G_DcAdaptiveAlpha) * sample;
                            G_DcUpdateCount = G_DcUpdateCount + 1;
                            dc_error = abs(sample - old_dc_value);
                            G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = dc_error;
                        otherwise
                            % 기본값: Enhanced 방법
                            [G_wRxReferValue, G_DcState] = update_dc_offset_enhanced(G_wRxReferValue, sample, alpha_dc, G_SignalQuality, G_DcState);
                            if G_DcState.updated
                                G_DcUpdateCount = G_DcUpdateCount + 1;
                                G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = G_DcState.error;
                            end
                    end
                end
            end

            G_wRxPrevBitD = G_wRxCurrBitD;

            switch (G_wRxRunStatus)
                case RX_MDM_STATUS_START
                    % 개선된 시작 비트 검출 (더 유연한 패턴 매칭)
                    start_pattern_detected = false;

                    % 다양한 시작 패턴 검사 (노이즈에 더 강건)
                    if (bitand(G_wRxShiftReg, 0x003f) == 0x003e || ...
                        bitand(G_wRxShiftReg, 0x001f) == 0x001e || ...
                        bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        start_pattern_detected = true;
                    end

                    if start_pattern_detected
                        %%----------------------------------------------------------
                        if (ENABLE_PLOT2 == 1)
                            h_fig2 = figure(2);
                            h_fig2.Name = 'Detected Start Data';
                            x1 = G_PreStart:idx;
                            x2 = G_PreStart:idx;
                            subplot(2,1,1); plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, AdativeDcOffset(x1), '-m'); grid; 
                                            title('detected\_start\_data');
                            subplot(2,1,2); plot(x2, G_pSrcDataCh1(x2), '-x', x1, G_pFilteredData(x1), '-o'); grid; 
                                            title('filtered\_start\_data');
                        end
                        %----------------------------------------------------------

                        G_wRxBitCount    = 0;
                        G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                    else
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= 25)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT97 == 1)
                                h_fig97 = figure(97);
                                h_fig97.Name = 'Start Bit Error';
                                x1 = G_PreStart:idx+100;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                xline(idx,'--', 'Current Positon');
                                title('Error Start Bit');
                            end
                            %----------------------------------------------------------

                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStartErrCnt = G_dStartErrCnt + 1;
                        end
                    end

                case RX_MDM_STATUS_PRELOAD
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if (G_wRxBitCount == 8)
                        G_wRxBitCount = 0;
                        G_wCrcRegData = 0xffff;
                        G_wRxRunStatus= RX_MDM_STATUS_DATA;
                        %ClrRxRawFormTemp();

                        nTemp = bitshift(G_wRxShiftReg, 2);
                        nTemp = bitand(nTemp, 0x00ff);
                        nMsgID = G_vReverDataTableX(nTemp + 1);
                        if (nMsgID < 0 || nMsgID > 27)
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dPloadErrCnt = G_dPloadErrCnt + 1;
                        else
                            m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                        end
                    end

                case RX_MDM_STATUS_DATA
                    if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= m_wRxMaxBitSize+7)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT98 == 1)
                                h_fig98 = figure(98);
                                h_fig98.Name = 'Stuffing Bit Error';
                                x1 = G_PreStart:idx;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Error Stuffing Bit');
                            end
                            %----------------------------------------------------------

                            %%%%%% ResetToRxStatusPreamble()
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_bRxByteData = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStuffErrCnt = G_dStuffErrCnt + 1;
                            continue;
                        end

                        G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                        G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                        G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

                        G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
                    end

                    if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    %if (bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                            %----------------------------------------------------------
                            if (ENABLE_PLOT3 == 1)
                                h_fig3 = figure(3);
                                h_fig3.Name = 'Received Data(CRC OK)';
                                x1 = G_PreStart:idx+50;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received AIS Packet');
                            end
                            %----------------------------------------------------------

                            if (G_PreStart > (215042-10) && G_PreStart < (215042+10))
                                break;
                            end

                            %WritePacketIntoRxRawBuff();
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_bRxByteData = 0;
                            G_dRcvPktCnt = G_dRcvPktCnt + 1;
                        else
                            %----------------------------------------------------------
                            if (ENABLE_PLOT99 == 1)
                                h_fig99 = figure(99);
                                h_fig99.Name = 'Received Data(CRC ERROR)';
                                x1 = G_PreStart:idx;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received Error AIS Packet');
                            end
                            %----------------------------------------------------------

                            %if (G_PreStart > (426187-10) && G_PreStart < (426187+10))
                            %    break;
                            %end

                            %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                            %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                            G_wRxBitCount    = 0;
                            %G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                            G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                            G_bRxByteData    = 0;
                            G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                            G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;

                            % CRC 에러 패킷의 Raw data 저장 (파일 저장이 활성화된 경우에만)
                            if (ENABLE_FILE_SAVE == 1) && (G_dCrcErrCnt <= MAX_CRC_ERROR_PACKETS)
                                % 패킷 시작과 끝 인덱스 계산
                                packet_start_idx = max(1, G_PreStart - 500);  % 프리앰블 포함하여 앞쪽 여유분
                                packet_end_idx = min(length(G_pSrcDataCh1), idx + 100);  % 뒤쪽 여유분

                                % Raw data 추출
                                if ENABLE_GMSK_RX_FLT > 0
                                    crc_error_raw_data = G_pFilteredData(packet_start_idx:packet_end_idx);
                                    original_raw_data = G_pSrcDataCh1(packet_start_idx:packet_end_idx);
                                else
                                    crc_error_raw_data = G_pSrcDataCh1(packet_start_idx:packet_end_idx);
                                    original_raw_data = crc_error_raw_data;
                                end

                                % 패킷 정보 저장 (파일 저장이 활성화된 경우에만)
                                if (ENABLE_FILE_SAVE == 1)
                                    G_CrcErrorPackets{G_dCrcErrCnt} = struct(...
                                        'raw_data', crc_error_raw_data, ...
                                        'original_data', original_raw_data, ...
                                        'start_idx', packet_start_idx, ...
                                        'end_idx', packet_end_idx, ...
                                        'preamble_idx', G_PreStart, ...
                                        'packet_length', packet_end_idx - packet_start_idx + 1, ...
                                        'crc_error_time', idx, ...
                                        'signal_quality', G_SignalQuality, ...
                                        'dc_reference', G_wRxReferValue);

                                    G_CrcErrorStartIdx(G_dCrcErrCnt) = packet_start_idx;
                                    G_CrcErrorEndIdx(G_dCrcErrCnt) = packet_end_idx;

                                    fprintf('CRC Error Packet #%d saved: Start=%d, End=%d, Length=%d\n', ...
                                        G_dCrcErrCnt, packet_start_idx, packet_end_idx, packet_end_idx - packet_start_idx + 1);
                                else
                                    fprintf('CRC Error Packet #%d detected (not saved): Start=%d, End=%d, Length=%d\n', ...
                                        G_dCrcErrCnt, packet_start_idx, packet_end_idx, packet_end_idx - packet_start_idx + 1);
                                end
                            end
                        end
                    end

                otherwise
                    warning('Unexpected run status.');
            end
        end
        G_dStartSymbolIndex = nCurSymbolIdx+1;
    end
end

%-------------------------------------------------------------------------
% DC 성능 분석 리포트
method_names = {'Enhanced', 'Kalman', 'HPF', 'Original'};
if G_DcUpdateCount > 0
    analyze_dc_performance(G_DcErrorHistory, G_DcUpdateCount, method_names{G_DcMethod});
end

% 수신 성능 통계
figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.6350 0.0780 0.1840];
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')

% 성능 개선 정보 출력
fprintf('\n=== AIS Receiver Performance Summary ===\n');
fprintf('DC Method Used: %s\n', method_names{G_DcMethod});
fprintf('Total Packets Received: %d\n', G_dRcvPktCnt);
fprintf('Success Rate: %.2f%%\n', G_dRcvPktCnt / (G_dRcvPktCnt + G_dCrcErrCnt) * 100);
fprintf('DC Updates: %d\n', G_DcUpdateCount);

% Viterbi MLSD 성능 정보 출력
if G_ViterbiEnabled == 1
    fprintf('\n=== Viterbi MLSD Performance ===\n');
    fprintf('Viterbi MLSD Enabled: Yes\n');
    fprintf('Channel Estimations: %d\n', G_ChannelEstimationCount);
    fprintf('Channel Coefficients: h0=%.4f, h1=%.4f, bias=%.4f\n', G_ChannelH0, G_ChannelH1, G_ChannelBias);
    fprintf('Viterbi Detections: %d\n', G_ViterbiDetectionCount);

    if G_ViterbiStats.total_count > 0
        viterbi_success_rate = G_ViterbiStats.success_count / G_ViterbiStats.total_count * 100;
        fprintf('Viterbi Success Rate: %.2f%% (%d/%d)\n', viterbi_success_rate, G_ViterbiStats.success_count, G_ViterbiStats.total_count);
    end

    % Viterbi 신뢰도 통계
    if sum(G_ViterbiConfidenceHistory) > 0
        valid_confidences = G_ViterbiConfidenceHistory(G_ViterbiConfidenceHistory > 0);
        if ~isempty(valid_confidences)
            avg_viterbi_confidence = mean(valid_confidences);
            fprintf('Average Viterbi Confidence: %.3f\n', avg_viterbi_confidence);
            G_ViterbiStats.avg_confidence = avg_viterbi_confidence;
        end
    end

    fprintf('Window Size: %d symbols\n', VITERBI_WINDOW_SIZE);
    fprintf('Traceback Depth: %d symbols\n', VITERBI_TRACEBACK_DEPTH);
else
    fprintf('\n=== Viterbi MLSD Performance ===\n');
    fprintf('Viterbi MLSD Enabled: No (using conventional NRZI decoding)\n');
end

% 기존 방법 최적화 정보 (Method 4인 경우)
if G_DcMethod == 4
    fprintf('\n=== Original Method Optimization Info ===\n');
    fprintf('Final Adaptive Alpha: %.4f\n', G_DcAdaptiveAlpha);
    fprintf('DC Stability Counter: %d\n', G_DcStabilityCounter);
    fprintf('DC Variance: %.6f\n', var(G_DcVarianceWindow));
    fprintf('Average Signal Quality: %.3f\n', G_SignalQuality);
end

%% CRC 에러 패킷 Raw data 저장 (폴더 생성 및 연속 파일 저장)
if G_dCrcErrCnt > 0 && ENABLE_FILE_SAVE == 1
    fprintf('\n=== CRC Error Packets Raw Data Export ===\n');
    fprintf('Total CRC Error Packets: %d\n', G_dCrcErrCnt);

    % 현재 시간을 파일명에 포함
    current_time = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));

    % CRC 에러 전용 폴더 생성
    crc_error_folder = sprintf('CRC_Error_Data_%s', current_time);
    if ~exist(crc_error_folder, 'dir')
        mkdir(crc_error_folder);
        fprintf('Created folder: %s\n', crc_error_folder);
    end

    % 모든 CRC 에러 패킷을 하나의 연속된 데이터로 결합
    % 전체 데이터 크기 추정 (사전할당을 위해)
    total_estimated_samples = 0;
    valid_packet_count = 0;
    if MAX_CRC_ERROR_PACKETS > 0
        for i = 1:min(G_dCrcErrCnt, MAX_CRC_ERROR_PACKETS)
            if ~isempty(G_CrcErrorPackets{i})
                total_estimated_samples = total_estimated_samples + G_CrcErrorPackets{i}.packet_length;
                valid_packet_count = valid_packet_count + 1;
            end
        end
    end

    % 배열 사전할당
    all_raw_data_original = zeros(total_estimated_samples, 1);
    all_raw_data_filtered = zeros(total_estimated_samples, 1);
    packet_boundaries = zeros(valid_packet_count, 2);  % 각 패킷의 경계 정보

    % 구조체 배열 사전할당
    packet_info_list = struct(...
        'packet_id', cell(valid_packet_count, 1), ...
        'original_start_idx', cell(valid_packet_count, 1), ...
        'original_end_idx', cell(valid_packet_count, 1), ...
        'preamble_idx', cell(valid_packet_count, 1), ...
        'packet_length', cell(valid_packet_count, 1), ...
        'crc_error_time', cell(valid_packet_count, 1), ...
        'signal_quality', cell(valid_packet_count, 1), ...
        'dc_reference', cell(valid_packet_count, 1), ...
        'continuous_start', cell(valid_packet_count, 1), ...
        'continuous_end', cell(valid_packet_count, 1));

    current_position = 1;
    valid_idx = 1;

    if MAX_CRC_ERROR_PACKETS > 0
        for i = 1:min(G_dCrcErrCnt, MAX_CRC_ERROR_PACKETS)
            if ~isempty(G_CrcErrorPackets{i})
                packet_info = G_CrcErrorPackets{i};

                % 연속 데이터에 추가 (인덱스 기반으로 효율적으로)
                packet_end_position = current_position + packet_info.packet_length - 1;
                all_raw_data_original(current_position:packet_end_position) = packet_info.original_data(:);
                all_raw_data_filtered(current_position:packet_end_position) = packet_info.raw_data(:);

                % 패킷 경계 정보 저장
                packet_boundaries(valid_idx, :) = [current_position, packet_end_position];

                % 패킷 정보 저장
                packet_info_list(valid_idx).packet_id = i;
                packet_info_list(valid_idx).original_start_idx = packet_info.start_idx;
                packet_info_list(valid_idx).original_end_idx = packet_info.end_idx;
                packet_info_list(valid_idx).preamble_idx = packet_info.preamble_idx;
                packet_info_list(valid_idx).packet_length = packet_info.packet_length;
                packet_info_list(valid_idx).crc_error_time = packet_info.crc_error_time;
                packet_info_list(valid_idx).signal_quality = packet_info.signal_quality;
                packet_info_list(valid_idx).dc_reference = packet_info.dc_reference;
                packet_info_list(valid_idx).continuous_start = current_position;
                packet_info_list(valid_idx).continuous_end = packet_end_position;

                fprintf('Packet #%d added to continuous data: Length=%d, Position=%d-%d\n', ...
                    i, packet_info.packet_length, current_position, packet_end_position);

                current_position = packet_end_position + 1;
                valid_idx = valid_idx + 1;
            end
        end
    end

    % 연속된 CRC 에러 데이터를 파일로 저장
    continuous_filename_mat = fullfile(crc_error_folder, sprintf('CRC_Error_Continuous_%s.mat', current_time));
    continuous_filename_csv = fullfile(crc_error_folder, sprintf('CRC_Error_Continuous_%s.csv', current_time));
    continuous_filename_bin = fullfile(crc_error_folder, sprintf('CRC_Error_Continuous_%s.bin', current_time));

    % MAT 파일로 저장 (모든 정보 포함)
    CRC_Error_Continuous = struct();
    CRC_Error_Continuous.original_data = all_raw_data_original;
    CRC_Error_Continuous.filtered_data = all_raw_data_filtered;
    CRC_Error_Continuous.packet_boundaries = packet_boundaries;
    CRC_Error_Continuous.packet_info = packet_info_list;
    CRC_Error_Continuous.total_packets = G_dCrcErrCnt;
    CRC_Error_Continuous.total_samples = length(all_raw_data_original);
    CRC_Error_Continuous.export_time = current_time;
    CRC_Error_Continuous.source_file = 'ais_receiver_optimized_final_v2.m';
    CRC_Error_Continuous.description = 'Continuous CRC error packets raw data';

    save(continuous_filename_mat, 'CRC_Error_Continuous');

    % CSV 파일로 저장 (원본 데이터, 필터링된 데이터)
    writematrix([all_raw_data_original, all_raw_data_filtered], continuous_filename_csv);

    % 바이너리 파일로 저장 (원본 데이터만, 16비트 정수)
    fid = fopen(continuous_filename_bin, 'wb');
    if fid ~= -1
        % 원본 데이터를 16비트 정수로 변환하여 저장
        original_data_int16 = int16((all_raw_data_original + 0.5) * 4095);  % 0~1 범위를 0~4095로 변환
        fwrite(fid, original_data_int16, 'int16');
        fclose(fid);
    end

    % 개별 패킷 파일들도 폴더에 저장
    individual_folder = fullfile(crc_error_folder, 'Individual_Packets');
    if ~exist(individual_folder, 'dir')
        mkdir(individual_folder);
    end

    if MAX_CRC_ERROR_PACKETS > 0
        for i = 1:min(G_dCrcErrCnt, MAX_CRC_ERROR_PACKETS)
            if ~isempty(G_CrcErrorPackets{i})
                packet_info = G_CrcErrorPackets{i};

                % 개별 파일명 생성
                individual_filename_mat = fullfile(individual_folder, sprintf('CRC_Error_Packet_%02d.mat', i));
                individual_filename_csv = fullfile(individual_folder, sprintf('CRC_Error_Packet_%02d.csv', i));

                % MAT 파일로 저장
                crc_error_packet = packet_info;
                save(individual_filename_mat, 'crc_error_packet');

                % CSV 파일로 저장
                writematrix([packet_info.original_data(:), packet_info.raw_data(:)], individual_filename_csv);
            end
        end
    end

    % 요약 정보 파일 생성
    summary_filename = fullfile(crc_error_folder, sprintf('CRC_Error_Summary_%s.txt', current_time));
    fid = fopen(summary_filename, 'w');
    if fid ~= -1
        fprintf(fid, '=== CRC Error Packets Summary ===\n');
        fprintf(fid, 'Export Time: %s\n', current_time);
        fprintf(fid, 'Total CRC Error Packets: %d\n', G_dCrcErrCnt);
        fprintf(fid, 'Total Continuous Samples: %d\n', length(all_raw_data_original));
        fprintf(fid, 'Average Packet Length: %.1f samples\n', mean([packet_info_list.packet_length]));
        fprintf(fid, '\n=== Packet Details ===\n');
        for i = 1:length(packet_info_list)
            fprintf(fid, 'Packet %02d: Start=%d, End=%d, Length=%d, Preamble=%d, Quality=%.3f\n', ...
                i, packet_info_list(i).original_start_idx, packet_info_list(i).original_end_idx, ...
                packet_info_list(i).packet_length, packet_info_list(i).preamble_idx, ...
                packet_info_list(i).signal_quality);
        end
        fprintf(fid, '\n=== File Information ===\n');
        fprintf(fid, 'Continuous MAT file: %s\n', continuous_filename_mat);
        fprintf(fid, 'Continuous CSV file: %s\n', continuous_filename_csv);
        fprintf(fid, 'Continuous BIN file: %s\n', continuous_filename_bin);
        fprintf(fid, 'Individual packets folder: %s\n', individual_folder);
        fclose(fid);
    end

    % 결과 출력
    fprintf('\n=== CRC Error Data Export Results ===\n');
    fprintf('Folder created: %s\n', crc_error_folder);
    fprintf('Continuous data saved: %s\n', continuous_filename_mat);
    fprintf('Total continuous samples: %d\n', length(all_raw_data_original));
    fprintf('Individual packets saved in: %s\n', individual_folder);
    fprintf('Summary file: %s\n', summary_filename);
    fprintf('Average packet length: %.1f samples\n', mean([packet_info_list.packet_length]));
    fprintf('CRC Error Raw Data Export Complete!\n');
elseif G_dCrcErrCnt > 0 && ENABLE_FILE_SAVE == 0
    fprintf('\n=== CRC Error Packets Detected (File Save Disabled) ===\n');
    fprintf('Total CRC Error Packets: %d\n', G_dCrcErrCnt);
    fprintf('File saving is disabled (ENABLE_FILE_SAVE = 0).\n');
    fprintf('To save CRC error data, set ENABLE_FILE_SAVE = 1.\n');
else
    fprintf('\n=== No CRC Error Packets Found ===\n');
    fprintf('All packets were successfully received without CRC errors.\n');
end

%-------------------------------------------------------------------------

function [h0, bias] = calc_coefficient(received_data, source_data, half_impulse_response)
    INDEX_START = 1;

    %received_data = received_data - 0.1;
    A = [source_data(INDEX_START:length(received_data))', ones(length(received_data),1)];

    pinv_data = pinv(A);
    received_data = received_data';
    coeff_vector = pinv_data*received_data;
    h0 = coeff_vector(1)*max(half_impulse_response);
    bias = coeff_vector(2);

    figure(34)
    kkk = 1:length(received_data);
    scale = h0/max(half_impulse_response);
    subplot(4,1,1); plot(received_data); grid; title('received\_data');
    subplot(4,1,2); plot(source_data(INDEX_START:INDEX_START+length(received_data)-1)); grid; title('source\_data');
    subplot(4,1,3); plot(kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1), '-o', kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1)+bias, '-x', kkk, received_data, '-+'); grid; title('source\_data (o), received\_data (x)');
    subplot(4,1,4); plot(kkk, pinv_data(1,:), '-o', kkk, pinv_data(2,:), '-x'); grid; title('test');
end